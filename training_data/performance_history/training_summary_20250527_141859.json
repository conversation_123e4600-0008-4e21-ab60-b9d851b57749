{"training_date": "2025-05-27T14:18:59.336328", "initial_accuracy": 0.6026766390106232, "final_accuracy": 0.6821591710211943, "accuracy_improvement": 0.0794825320105711, "total_iterations": 5, "best_iteration": 1, "training_results": [{"iteration": 1, "accuracy": 0.6821591710211943, "improvement": 0.07948253201057105, "metrics": "TrainingMetrics(accuracy=np.float64(0.6821591710211943), precision=np.float64(0.6480512124701345), recall=np.float64(0.6275864373394988), f1_score=np.float64(0.6344080290497107), confidence_score=np.float64(0.600300070498651), processing_time=2.0, quality_improvement=0.07948253201057105)", "focus_areas": ["general_improvement", "extreme_enhancement"]}, {"iteration": 2, "accuracy": 0.6620499907096549, "improvement": 0.05937335169903164, "metrics": "TrainingMetrics(accuracy=np.float64(0.6620499907096549), precision=np.float64(0.6289474911741721), recall=np.float64(0.6090859914528826), f1_score=np.float64(0.615706491359979), confidence_score=np.float64(0.5826039918244963), processing_time=2.0, quality_improvement=0.05937335169903164)", "focus_areas": ["general_improvement", "extreme_enhancement"]}, {"iteration": 3, "accuracy": 0.6227623424236488, "improvement": 0.020085703413025593, "metrics": "TrainingMetrics(accuracy=np.float64(0.6227623424236488), precision=np.float64(0.5916242253024664), recall=np.float64(0.572941355029757), f1_score=np.float64(0.5791689784539934), confidence_score=np.float64(0.548030861332811), processing_time=2.0, quality_improvement=0.020085703413025593)", "focus_areas": ["general_improvement", "extreme_enhancement"]}, {"iteration": 4, "accuracy": 0.6800974538556332, "improvement": 0.07742081484501004, "metrics": "TrainingMetrics(accuracy=np.float64(0.6800974538556332), precision=np.float64(0.6460925811628515), recall=np.float64(0.6256896575471826), f1_score=np.float64(0.632490632085739), confidence_score=np.float64(0.5984857593929572), processing_time=2.0, quality_improvement=0.07742081484501004)", "focus_areas": ["general_improvement", "extreme_enhancement"]}, {"iteration": 5, "accuracy": 0.6739683867297884, "improvement": 0.07129174771916522, "metrics": "TrainingMetrics(accuracy=np.float64(0.6739683867297884), precision=np.float64(0.640269967393299), recall=np.float64(0.6200509157914054), f1_score=np.float64(0.6267905996587033), confidence_score=np.float64(0.5930921803222138), processing_time=2.0, quality_improvement=0.07129174771916522)", "focus_areas": ["general_improvement", "extreme_enhancement"]}]}