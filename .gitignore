# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Training Data (keep structure, ignore content)
training_data/raw_images/*.jpg
training_data/raw_images/*.png
training_data/processed_images/*.jpg
training_data/processed_images/*.png
training_data/sessions/*/
training_data/*.db

# Test Results
test_results/
extreme_test_results/
extreme_ocr_results/

# Temporary Files
*.tmp
*.log
*~

# Model Files (large files)
*.pkl
*.h5
*.pth
*.onnx

# Environment Variables
.env
.env.local
