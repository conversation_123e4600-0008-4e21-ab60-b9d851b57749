<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            text-align: center;
        }
        
        .header h1 {
            color: #4a5568;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        
        .content-section {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .section-title {
            color: #667eea;
            font-size: 1.8em;
            margin-bottom: 20px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .step {
            background: #f7fafc;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        
        .step-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        
        .step-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 10px;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .tip {
            background: #c6f6d5;
            border: 1px solid #68d391;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .tip::before {
            content: "💡 ";
            font-weight: bold;
        }
        
        .warning {
            background: #fed7d7;
            border: 1px solid #fc8181;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .warning::before {
            content: "⚠️ ";
            font-weight: bold;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: #f7fafc;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
        }
        
        .feature-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        ul {
            margin-left: 20px;
            margin-bottom: 15px;
        }
        
        li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎓 AI Training Guide</h1>
            <p>Complete guide to training your AI models for 95%+ accuracy</p>
        </div>
        
        <div class="content-section">
            <h2 class="section-title">🎯 Training Overview</h2>
            <p>This guide will help you achieve 95%+ accuracy for Cambodian ID card OCR using our advanced AI training system. You have two powerful options for training your models:</p>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <h3>Flutter Camera Training</h3>
                    <p>Real-time training using your mobile app camera. Perfect for field data collection and immediate model improvement.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🌐</div>
                    <h3>Web Dashboard Training</h3>
                    <p>Comprehensive web-based training interface with analytics, batch processing, and detailed monitoring.</p>
                </div>
            </div>
        </div>
        
        <div class="content-section">
            <h2 class="section-title">🚀 Quick Start</h2>
            
            <div class="step">
                <span class="step-number">1</span>
                <div>
                    <div class="step-title">Choose Your Training Method</div>
                    <p>Decide whether you want to use Flutter camera integration or the web dashboard for training.</p>
                    <ul>
                        <li><strong>Flutter</strong>: Best for mobile apps and real-time training</li>
                        <li><strong>Web Dashboard</strong>: Best for desktop analysis and bulk processing</li>
                    </ul>
                </div>
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <div>
                    <div class="step-title">Start a Training Session</div>
                    <p>Create a new training session with your target accuracy and sample goals.</p>
                    <div class="code-block">
POST /training/session/start
{
  "session_name": "Cambodian ID Training v1",
  "target_accuracy": 0.95,
  "max_samples": 100
}
                    </div>
                </div>
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <div>
                    <div class="step-title">Collect Training Data</div>
                    <p>Add high-quality ID card images with correct field values (ground truth).</p>
                    <div class="tip">
                        Start with 20-30 clear, well-lit images before adding challenging samples.
                    </div>
                </div>
            </div>
            
            <div class="step">
                <span class="step-number">4</span>
                <div>
                    <div class="step-title">Monitor Progress</div>
                    <p>Track your training progress and model accuracy in real-time.</p>
                    <ul>
                        <li>Overall accuracy and field-specific performance</li>
                        <li>Training sample count and quality distribution</li>
                        <li>Processing time and efficiency metrics</li>
                    </ul>
                </div>
            </div>
            
            <div class="step">
                <span class="step-number">5</span>
                <div>
                    <div class="step-title">Deploy Your Model</div>
                    <p>Once you reach your target accuracy, deploy the model for production use.</p>
                    <div class="code-block">
POST /training/model/deploy/{session_id}
{
  "model_name": "Cambodian ID OCR v2.0"
}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="content-section">
            <h2 class="section-title">🎯 Training Best Practices</h2>
            
            <h3>📸 Image Quality Guidelines</h3>
            <ul>
                <li><strong>Resolution</strong>: Minimum 800x600 pixels for ID cards</li>
                <li><strong>Lighting</strong>: Even, natural lighting without shadows</li>
                <li><strong>Focus</strong>: Sharp, clear text with no motion blur</li>
                <li><strong>Angle</strong>: Straight-on view, minimal perspective distortion</li>
                <li><strong>Background</strong>: Clean, contrasting background</li>
            </ul>
            
            <h3>📊 Data Collection Strategy</h3>
            <ul>
                <li><strong>Quality over Quantity</strong>: 50 high-quality samples > 200 poor samples</li>
                <li><strong>Diverse Conditions</strong>: Include various lighting, angles, and qualities</li>
                <li><strong>Field Balance</strong>: Ensure all fields (name, ID, date) are represented</li>
                <li><strong>Edge Cases</strong>: Include challenging samples gradually</li>
            </ul>
            
            <div class="tip">
                Use the active learning feature to automatically select the most valuable training samples!
            </div>
        </div>
        
        <div class="content-section">
            <h2 class="section-title">📱 Flutter Integration</h2>
            <p>For mobile app developers who want to integrate AI training directly into their Flutter applications:</p>
            
            <div class="code-block">
// Start training session
final sessionId = await trainingService.startTrainingSession(
  sessionName: 'Mobile Training Session',
  targetAccuracy: 0.95,
  maxSamples: 100,
);

// Add camera training data
await trainingService.addCameraTrainingData(
  sessionId: sessionId,
  imageFile: capturedImage,
  groundTruth: correctFieldValues,
);
            </div>
            
            <p><a href="/static/FLUTTER_INTEGRATION_GUIDE.md" class="btn">📖 Complete Flutter Guide</a></p>
        </div>
        
        <div class="content-section">
            <h2 class="section-title">🌐 Web Dashboard</h2>
            <p>For comprehensive training management and analysis:</p>
            
            <ul>
                <li><strong>Real-time Monitoring</strong>: Live training progress and metrics</li>
                <li><strong>Analytics Dashboard</strong>: Performance trends and insights</li>
                <li><strong>Session Management</strong>: Create, monitor, and manage training sessions</li>
                <li><strong>Batch Processing</strong>: Upload multiple images efficiently</li>
                <li><strong>AI Recommendations</strong>: Get suggestions for improvement</li>
            </ul>
            
            <p><a href="/ui/dashboard" class="btn">🚀 Open Training Dashboard</a></p>
        </div>
        
        <div class="content-section">
            <h2 class="section-title">⚡ Advanced Features</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🤖 Active Learning</h4>
                    <p>Automatically selects the most valuable samples for training to maximize accuracy improvement.</p>
                </div>
                <div class="feature-card">
                    <h4>📊 Quality Assessment</h4>
                    <p>Automatic image quality analysis to ensure optimal training data selection.</p>
                </div>
                <div class="feature-card">
                    <h4>🔄 Real-time Improvement</h4>
                    <p>Continuous model enhancement with immediate feedback and progress tracking.</p>
                </div>
                <div class="feature-card">
                    <h4>🎯 Khmer Optimization</h4>
                    <p>Specialized training techniques optimized for Cambodian script and ID card layouts.</p>
                </div>
            </div>
        </div>
        
        <div class="content-section">
            <h2 class="section-title">🔧 Troubleshooting</h2>
            
            <h3>Common Issues</h3>
            
            <div class="warning">
                <strong>Low Accuracy:</strong> If your model accuracy is below 85%, try adding more high-quality training samples with diverse conditions.
            </div>
            
            <div class="warning">
                <strong>Slow Training:</strong> Large batch sizes or poor quality images can slow training. Use the quality assessment feature to filter samples.
            </div>
            
            <div class="warning">
                <strong>Field-Specific Issues:</strong> If certain fields (like Khmer names) have low accuracy, focus on collecting more samples for those specific fields.
            </div>
            
            <h3>Getting Help</h3>
            <ul>
                <li><strong>API Documentation</strong>: <a href="/docs">/docs</a></li>
                <li><strong>Health Check</strong>: <a href="/health">/health</a></li>
                <li><strong>Training Metrics</strong>: Monitor real-time performance in the dashboard</li>
            </ul>
        </div>
        
        <div class="content-section">
            <h2 class="section-title">🎉 Success Tips</h2>
            
            <div class="tip">
                <strong>Start Small:</strong> Begin with 20-30 perfect samples, then gradually add challenging cases.
            </div>
            
            <div class="tip">
                <strong>Monitor Continuously:</strong> Use the real-time dashboard to track progress and identify issues early.
            </div>
            
            <div class="tip">
                <strong>Balance Your Data:</strong> Ensure all ID card fields are well represented in your training set.
            </div>
            
            <div class="tip">
                <strong>Use Active Learning:</strong> Let the AI help you select the most valuable training samples.
            </div>
            
            <p style="text-align: center; margin-top: 30px;">
                <a href="/ui/dashboard" class="btn">🚀 Start Training Now</a>
            </p>
        </div>
    </div>
</body>
</html>
