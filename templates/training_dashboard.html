<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            color: #4a5568;
            margin-bottom: 10px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            text-align: center;
        }
        
        .stat-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #718096;
            font-size: 0.9em;
        }
        
        .sessions-section {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin-bottom: 20px;
        }
        
        .session-card {
            background: #f7fafc;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
        }
        
        .session-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .session-name {
            font-weight: bold;
            color: #2d3748;
        }
        
        .session-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-active {
            background: #c6f6d5;
            color: #22543d;
        }
        
        .status-completed {
            background: #bee3f8;
            color: #2a4365;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .chart-container {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin-bottom: 20px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #718096;
        }
        
        .error {
            background: #fed7d7;
            color: #c53030;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI Training Dashboard</h1>
            <p>Monitor and manage your AI model training sessions</p>
        </div>
        
        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <div class="stat-value" id="totalSessions">-</div>
                <div class="stat-label">Total Sessions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="activeSessions">-</div>
                <div class="stat-label">Active Sessions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avgAccuracy">-</div>
                <div class="stat-label">Average Accuracy</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalSamples">-</div>
                <div class="stat-label">Training Samples</div>
            </div>
        </div>
        
        <div class="sessions-section">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2>Recent Training Sessions</h2>
                <button class="btn" onclick="createNewSession()">+ New Session</button>
            </div>
            <div id="sessionsList">
                <div class="loading">Loading sessions...</div>
            </div>
        </div>
        
        <div class="chart-container">
            <h3>Training Progress Overview</h3>
            <canvas id="progressChart" width="400" height="200"></canvas>
        </div>
    </div>

    <script>
        // Dashboard functionality
        let chart = null;
        
        async function loadDashboardData() {
            try {
                // Load dashboard stats
                const statsResponse = await fetch('/ui/api/dashboard/stats');
                const stats = await statsResponse.json();
                
                document.getElementById('totalSessions').textContent = stats.total_sessions;
                document.getElementById('activeSessions').textContent = stats.active_sessions;
                document.getElementById('avgAccuracy').textContent = (stats.average_accuracy * 100).toFixed(1) + '%';
                document.getElementById('totalSamples').textContent = stats.total_training_samples;
                
                // Load recent sessions
                const sessionsResponse = await fetch('/ui/api/sessions/recent');
                const sessionsData = await sessionsResponse.json();
                
                displaySessions(sessionsData.sessions);
                
                // Load analytics for chart
                const analyticsResponse = await fetch('/ui/api/analytics/overview');
                const analytics = await analyticsResponse.json();
                
                updateChart(analytics.accuracy_trend);
                
            } catch (error) {
                console.error('Failed to load dashboard data:', error);
                document.getElementById('sessionsList').innerHTML = 
                    '<div class="error">Failed to load sessions. Please refresh the page.</div>';
            }
        }
        
        function displaySessions(sessions) {
            const sessionsList = document.getElementById('sessionsList');
            
            if (sessions.length === 0) {
                sessionsList.innerHTML = '<div class="loading">No training sessions found. Create your first session!</div>';
                return;
            }
            
            sessionsList.innerHTML = sessions.map(session => `
                <div class="session-card">
                    <div class="session-header">
                        <div class="session-name">${session.name}</div>
                        <div class="session-status status-${session.status}">${session.status}</div>
                    </div>
                    <div>
                        <strong>Accuracy:</strong> ${session.accuracy_percentage}% | 
                        <strong>Samples:</strong> ${session.current_samples}/${session.max_samples}
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${session.progress_percentage}%"></div>
                    </div>
                    <small>Created: ${new Date(session.created_at).toLocaleDateString()}</small>
                </div>
            `).join('');
        }
        
        function updateChart(accuracyTrend) {
            const ctx = document.getElementById('progressChart').getContext('2d');
            
            if (chart) {
                chart.destroy();
            }
            
            chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: accuracyTrend.map(item => `Session ${item.session}`),
                    datasets: [{
                        label: 'Accuracy',
                        data: accuracyTrend.map(item => item.accuracy * 100),
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }
        
        async function createNewSession() {
            const name = prompt('Enter session name:');
            if (!name) return;
            
            const targetAccuracy = prompt('Enter target accuracy (0.0-1.0):', '0.95');
            const maxSamples = prompt('Enter maximum samples:', '100');
            
            try {
                const response = await fetch(`/ui/api/session/create?name=${encodeURIComponent(name)}&target_accuracy=${targetAccuracy}&max_samples=${maxSamples}`, {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert(`Session "${name}" created successfully!`);
                    loadDashboardData(); // Refresh data
                } else {
                    alert('Failed to create session: ' + result.message);
                }
            } catch (error) {
                alert('Failed to create session: ' + error.message);
            }
        }
        
        // Auto-refresh every 30 seconds
        setInterval(loadDashboardData, 30000);
        
        // Initial load
        loadDashboardData();
    </script>
</body>
</html>
